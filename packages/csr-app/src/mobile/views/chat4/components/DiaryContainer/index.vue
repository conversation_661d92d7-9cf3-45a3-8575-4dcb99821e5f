<template>
  <div class="diary-container">
    <!-- 标题栏 -->
    <div class="header">
      <div class="header-left">
        <button class="back-button" @click="handleGoBack">
          <DiaryBackIcon />
        </button>
      </div>
      <div class="header-title">
        <span class="title-text">Diary</span>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="divider"></div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 角色信息卡片 -->
      <div class="character-card">
        <div class="character-info">
          <!-- 角色大头像 -->
          <div class="character-avatar-large">
            <img
              :src="characterAvatar || '/default-avatar.png'"
              :alt="characterName"
              class="avatar-image"
            />
          </div>

          <!-- 角色详情 -->
          <div class="character-details">
            <div class="character-name">{{ characterName || 'Kurapika' }}</div>
            <div class="character-status">
              <div class="status-row">
                <div class="status-item">
                  <StatusLocationIcon class="status-icon" />
                  <span class="status-text">{{
                    diaryData.region || 'Bedroom'
                  }}</span>
                </div>
                <!-- <div class="status-item">
                  <StatusEmotionIcon class="status-icon" />
                  <span class="status-text">{{
                    diaryData.emotion || 'Calm'
                  }}</span>
                </div> -->
              </div>
              <div class="status-item activity">
                <StatusActivityIcon class="status-icon" />
                <span class="status-text">{{
                  diaryData.status || 'Sleeping'
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 好感度系统 -->
        <div class="favorability-section">
          <div class="favorability-header">
            <div class="favorability-label">
              <span class="favorability-title">Likability</span>
            </div>
            <div class="favorability-level">
              <span class="level-text">{{ currentLevelText }}</span>
            </div>
          </div>
          <div class="favorability-progress">
            <div class="progress-bar">
              <div class="progress-bg"></div>
              <div
                class="progress-fill"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
              <div
                class="progress-handle"
                :style="{ left: `calc(${progressPercentage}% - 8px)` }"
              >
                <DiaryHeartIcon />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日记本 -->
      <div class="diary-notebook">
        <!-- 日记本封皮 -->
        <div class="notebook-cover">
          <!-- 装订环 -->
          <div class="spiral-binding">
            <div v-for="i in 15" :key="i" class="spiral-ring"></div>
          </div>

          <!-- 日记页面容器 -->
          <div class="diary-page-container">
            <!-- 页面堆叠 -->
            <div class="page-stack">
              <!-- 底页（下一页预览） -->
              <div class="diary-page bottom-page">
                <div class="page-content">
                  <div class="diary-header">
                    <div class="diary-title">Inner monologue</div>
                    <div class="diary-meta">
                      <span class="diary-date">{{ currentDate }}</span>
                      <span class="diary-weather">{{ currentWeather }}</span>
                    </div>
                  </div>
                  <div class="diary-decoration">
                    <DiaryLineVector />
                  </div>
                  <div class="diary-content">
                    <div class="diary-lines">
                      <div class="diary-text next-page-preview">
                        {{ getPreviewContent() }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="page-shadow bottom-shadow"></div>
              </div>

              <!-- 翻页页面 -->
              <div
                class="diary-page flip-page"
                :class="{
                  'flipping': isFlipping,
                  'flip-forward': flipDirection === 'next',
                  'flip-backward': flipDirection === 'prev',
                }"
              >
                <!-- 页面正面 -->
                <div class="page-face front-face">
                  <div class="page-content">
                    <div class="diary-header">
                      <div class="diary-title">Inner monologue</div>
                      <div class="diary-meta">
                        <span class="diary-date">{{ currentDate }}</span>
                        <span class="diary-weather">{{ currentWeather }}</span>
                      </div>
                    </div>
                    <div class="diary-decoration">
                      <DiaryLineVector />
                    </div>

                    <!-- Loading 状态 -->
                    <div v-if="isLoading" class="diary-loading">
                      <div class="loading-text"
                        >{{ characterName }} is writing thoughts...</div
                      >
                      <div class="loading-dots">
                        <span></span><span></span><span></span>
                      </div>
                      <div class="loading-pen">
                        <div class="pen-tip"></div>
                        <div class="pen-body"></div>
                      </div>
                    </div>

                    <!-- 日记内容区域 -->
                    <div v-else class="diary-content">
                      <div class="diary-lines">
                        <div class="diary-text" ref="diaryTextRef">
                          {{ displayMessage }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="page-edge"></div> -->
                </div>

                <!-- 页面背面 -->
                <div class="page-face back-face">
                  <div class="page-content back-content">
                    <div class="back-lines"></div>
                  </div>
                  <div class="page-edge"></div>
                </div>

                <!-- 动态阴影 -->
                <!-- <div class="page-shadow dynamic-shadow"></div> -->
              </div>
            </div>
          </div>

          <!-- 翻页按钮 -->
          <div v-if="!isLoading && hasMultiplePages" class="page-controls">
            <button
              class="page-button prev"
              :disabled="currentPage === 0"
              @click="prevPage"
            >
              ‹
            </button>
            <span class="page-indicator"
              >{{ currentPage + 1 }} / {{ totalPages }}</span
            >
            <button
              class="page-button next"
              :disabled="currentPage === totalPages - 1"
              @click="nextPage"
            >
              ›
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch, nextTick } from 'vue'
import { useChat4Store } from '@/store/chat4'
import { useChatEventsStore } from '@/store/chat-events'
import { SceneUnlockUtils } from '@/types/favorability'
import DiaryBackIcon from '@/assets/icon/diary-back-icon.svg'
import DiaryLineVector from '@/assets/icon/diary-line-vector.svg'
import StatusLocationIcon from '@/assets/icon/status-location-icon.svg'
import StatusEmotionIcon from '@/assets/icon/status-emotion-icon.svg'
import StatusActivityIcon from '@/assets/icon/status-activity-icon.svg'
import DiaryHeartIcon from '@/assets/icon/diary-heart-icon.svg'

interface Props {
  characterName?: string
  characterAvatar?: string
}

interface Emits {
  (e: 'go-back'): void
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Kurapika',
  characterAvatar: '',
})

const emit = defineEmits<Emits>()

// Store
const chat4Store = useChat4Store()
const chatEventsStore = useChatEventsStore()

// 分页相关状态
const currentPage = ref(0)
const totalPages = ref(1)
const charsPerPage = 800 // 每页字符数
const diaryTextRef = ref<HTMLElement>()
const containerHeight = ref(0) // 容器高度
const lineHeight = 32 // 行高
const linesPerPage = ref(0) // 每页行数

// Loading状态
const isLoading = computed(() => {
  return !diaryData.value.content || diaryData.value.content.trim() === ''
})

// 多页内容处理
const hasMultiplePages = computed(() => {
  return totalPages.value > 1
})

// 获取好感度状态
const { levelInfos, currentHeartValue } = chatEventsStore.favorabilityState

// 从store获取diary数据
const diaryData = computed(() => chat4Store.diaryState)

// 计算当前等级
const currentLevel = computed(() => {
  return SceneUnlockUtils.calculateCurrentLevel(currentHeartValue, levelInfos)
})

// 计算当前等级显示文本
const currentLevelText = computed(() => {
  return SceneUnlockUtils.formatLevelText(currentLevel.value)
})

// 计算进度百分比
const progressPercentage = computed(() => {
  if (levelInfos.length === 0) return 0

  // 按好感度值排序
  const sortedLevels = [...levelInfos].sort(
    (a, b) => a.heart_value - b.heart_value,
  )

  // 找到当前等级和下一等级
  let currentLevelInfo = sortedLevels[0]
  let nextLevelInfo = sortedLevels[1]

  for (let i = 0; i < sortedLevels.length; i++) {
    if (currentHeartValue >= sortedLevels[i].heart_value) {
      currentLevelInfo = sortedLevels[i]
      nextLevelInfo = sortedLevels[i + 1]
    } else {
      break
    }
  }

  // 如果已经是最高等级
  if (!nextLevelInfo) {
    return 100
  }

  // 计算当前等级内的进度
  const currentLevelStart = currentLevelInfo.heart_value
  const nextLevelStart = nextLevelInfo.heart_value
  const progressInLevel = currentHeartValue - currentLevelStart
  const levelRange = nextLevelStart - currentLevelStart

  return Math.min(100, Math.max(0, (progressInLevel / levelRange) * 100))
})

// 计算每页实际能显示的字符数（基于容器高度）
const calculateCharsPerPage = () => {
  // 获取容器高度
  const container = document.querySelector('.diary-lines')
  if (!container) return charsPerPage

  const containerHeight = container.clientHeight
  const lineHeight = 32 // CSS中定义的行高
  const maxLines = Math.floor(containerHeight / lineHeight)

  // 估算每行能显示的字符数（基于字体大小和容器宽度）
  const fontSize = 12
  const containerWidth = container.clientWidth
  const charWidth = fontSize * 0.6 // 估算字符宽度
  const charsPerLine = Math.floor(containerWidth / charWidth)

  return Math.max(maxLines * charsPerLine, 400) // 最少400字符
}

// 计算总页数
const totalPagesComputed = computed(() => {
  const content = diaryData.value.content || ''
  if (!content) return 1

  // 使用动态计算的每页字符数
  const dynamicCharsPerPage = calculateCharsPerPage()
  return Math.ceil(content.length / dynamicCharsPerPage)
})

// 监听总页数变化
watch(
  totalPagesComputed,
  (newValue) => {
    totalPages.value = newValue
  },
  { immediate: true },
)

// 计算显示消息
const displayMessage = computed(() => {
  const content = diaryData.value.content || ''
  if (!content) return ''

  // 使用动态计算的每页字符数
  const dynamicCharsPerPage = calculateCharsPerPage()
  const startIndex = currentPage.value * dynamicCharsPerPage
  const endIndex = startIndex + dynamicCharsPerPage
  return content.slice(startIndex, endIndex)
})

// 计算日期显示
const currentDate = computed(() => {
  if (diaryData.value.date) {
    // 将日期格式化为 "March 6 | 2025" 格式
    const date = new Date(diaryData.value.date)
    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    }
    const formattedDate = date.toLocaleDateString('en-US', options)
    return formattedDate.replace(',', ' |')
  }
  return 'March 6 | 2025'
})

// 计算天气显示
const currentWeather = computed(() => {
  // 根据emotion映射天气
  const emotionWeatherMap: Record<string, string> = {
    happy: 'Sunny',
    sad: 'Rainy',
    angry: 'Stormy',
    calm: 'Cloudy',
    excited: 'Sunny',
    tired: 'Overcast',
  }

  if (diaryData.value.emotion && emotionWeatherMap[diaryData.value.emotion]) {
    return emotionWeatherMap[diaryData.value.emotion]
  }

  return 'Sunny'
})

// 处理返回按钮点击
const handleGoBack = () => {
  emit('go-back')
}

// 翻页动画状态
const isFlipping = ref(false)
const flipDirection = ref<'next' | 'prev'>('next')
const bottomPageContent = ref('') // 底页内容，避免翻页时内容闪烁

// 获取预览内容（下一页或上一页）
const getPreviewContent = () => {
  // 如果正在翻页，返回预设的底页内容
  if (isFlipping.value) {
    return bottomPageContent.value
  }

  const content = diaryData.value.content || ''
  if (!content) return ''

  let previewPage = currentPage.value
  if (
    flipDirection.value === 'next' &&
    currentPage.value < totalPages.value - 1
  ) {
    previewPage = currentPage.value + 1
  } else if (flipDirection.value === 'prev' && currentPage.value > 0) {
    previewPage = currentPage.value - 1
  }

  const dynamicCharsPerPage = calculateCharsPerPage()
  const startIndex = previewPage * dynamicCharsPerPage
  const endIndex = startIndex + dynamicCharsPerPage
  return content.slice(startIndex, endIndex)
}

// 翻页函数
const nextPage = async () => {
  if (currentPage.value < totalPages.value - 1 && !isFlipping.value) {
    // 设置底页内容为下一页内容
    const content = diaryData.value.content || ''
    const dynamicCharsPerPage = calculateCharsPerPage()
    const nextPageIndex = currentPage.value + 1
    const startIndex = nextPageIndex * dynamicCharsPerPage
    const endIndex = startIndex + dynamicCharsPerPage
    bottomPageContent.value = content.slice(startIndex, endIndex)

    flipDirection.value = 'next'
    isFlipping.value = true

    // 分阶段动画：抬起(150ms) -> 翻转(400ms) -> 落下(150ms)
    await new Promise((resolve) => setTimeout(resolve, 700))
    currentPage.value++
    isFlipping.value = false
  }
}

const prevPage = async () => {
  if (currentPage.value > 0 && !isFlipping.value) {
    // 设置底页内容为当前页内容（翻页后底页应该显示当前页）
    const content = diaryData.value.content || ''
    const dynamicCharsPerPage = calculateCharsPerPage()
    const currentPageIndex = currentPage.value
    const startIndex = currentPageIndex * dynamicCharsPerPage
    const endIndex = startIndex + dynamicCharsPerPage
    bottomPageContent.value = content.slice(startIndex, endIndex)

    flipDirection.value = 'prev'
    isFlipping.value = true

    // 分阶段动画
    await new Promise((resolve) => setTimeout(resolve, 700))
    currentPage.value--
    isFlipping.value = false
  }
}

// 监听内容变化，重置页码并重新计算分页
watch(
  () => diaryData.value.content,
  (newContent, oldContent) => {
    // 只有当内容真正改变时才重置页码
    if (newContent !== oldContent) {
      currentPage.value = 0
      initializePagination()
    }
  },
)

// 初始化分页计算
const initializePagination = () => {
  // 等待DOM渲染完成后计算分页
  nextTick(() => {
    // 重新计算总页数
    const content = diaryData.value.content || ''
    if (content) {
      const dynamicCharsPerPage = calculateCharsPerPage()
      totalPages.value = Math.ceil(content.length / dynamicCharsPerPage)
    }
  })
}

// 组件挂载时也重置页码，确保每次进入日记页面都从第一页开始
onMounted(() => {
  currentPage.value = 0
  initializePagination()
})
</script>

<style lang="less" scoped>
.diary-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 顶部状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  z-index: 10;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 17px 21px 12px;
  height: 100%;
}

.time {
  color: #000000;
  font-family: 'SF Pro', sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 20px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 17px;
  height: 11px;
  background: #000000;
  opacity: 0.4;
}

/* 标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  width: 16px;
  height: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12.8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  svg {
    color: rgba(0, 0, 0, 0.85);
  }
}

.header-title {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.title-text {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
}

/* 分割线 */
.divider {
  height: 1px;
  margin: 0 16px;
  background: rgba(255, 255, 255, 0.05);
  flex-shrink: 0;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
}

/* 角色信息卡片 */
.character-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.character-info {
  display: flex;
  gap: 12px;
}

.character-avatar-large {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.character-avatar-large .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-name {
  color: #000000;

  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  padding: 0 2px;
}

.character-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-row {
  display: flex;
  gap: 32px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.status-text {
  color: #000000;

  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.activity {
  align-self: stretch;
}

/* 好感度系统 */
.favorability-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  margin-left: auto;
}

.favorability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorability-title {
  color: #000000;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.favorability-level {
  display: flex;
  align-items: center;
  gap: 10px;
}

.level-text {
  color: #ff5b5b;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.favorability-progress {
  width: 100%;
  height: 14px;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 14px;
  position: relative;
}

.progress-bg {
  position: absolute;
  top: 3px;
  left: 0;
  width: 100%;
  height: 8px;
  background: #efefef;
  border-radius: 4px;
}

.progress-fill {
  position: absolute;
  top: 3px;
  left: 0;
  height: 8px;
  background: linear-gradient(90deg, #f273b9 0%, #c183fb 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-handle {
  position: absolute;
  top: -2px;
  transition: left 0.3s ease;
}

/* 日记本容器 */
.diary-notebook {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 12px;
  background: transparent;
}

/* 日记本封皮 */
.notebook-cover {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px 20px;
  position: relative;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 装订环 */
.spiral-binding {
  position: absolute;
  left: 16px;
  top: 32px;
  bottom: 32px;
  width: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.spiral-ring {
  width: 6px;
  height: 6px;
  background: rgba(183, 86, 255, 0.3);
  border: 1px solid rgba(183, 86, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(183, 86, 255, 0.2);
}

/* 日记页面容器 */
.diary-page-container {
  flex: 1;
  margin-left: 28px;
  perspective: 1200px;
  perspective-origin: left center;
}

/* 页面堆叠容器 */
.page-stack {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

/* 通用页面样式 */
.diary-page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(253, 251, 255, 0.98);
  border-radius: 0 16px 16px 0;
  border: 1px solid rgba(183, 86, 255, 0.1);
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 底页样式 */
.bottom-page {
  transform: translateZ(-2px);
  opacity: 0.95;
  box-shadow:
    0 4px 16px rgba(183, 86, 255, 0.05),
    0 1px 4px rgba(183, 86, 255, 0.03);
}

.bottom-shadow {
  position: absolute;
  top: 0;
  left: -2px;
  width: 4px;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(183, 86, 255, 0.15) 0%,
    transparent 100%
  );
  pointer-events: none;
}

/* 翻页页面 */
.flip-page {
  transform-origin: left center;
  transition: none;
  box-shadow:
    0 8px 32px rgba(183, 86, 255, 0.08),
    0 2px 8px rgba(183, 86, 255, 0.04);
}

/* 页面面容器 */
.page-face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* 正面 */
.front-face {
  transform: rotateY(0deg);
}

/* 背面 */
.back-face {
  transform: rotateY(180deg);
  background: rgba(248, 246, 252, 0.95);
}

/* 页面内容 */
.page-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 2;
  overflow: hidden;
  box-sizing: border-box;
}

.page-content.back-content {
  padding: 40px 20px;
}

/* 背面线条 */
.back-lines {
  width: 100%;
  height: 100%;
  background-image: repeating-linear-gradient(
    transparent,
    transparent 31px,
    rgba(183, 86, 255, 0.08) 31px,
    rgba(183, 86, 255, 0.08) 32px
  );
  opacity: 0.6;
}

/* 页面边缘厚度效果 */
.page-edge {
  position: absolute;
  top: 0;
  right: -3px;
  width: 3px;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(183, 86, 255, 0.2) 0%,
    rgba(183, 86, 255, 0.1) 50%,
    rgba(183, 86, 255, 0.05) 100%
  );
  border-radius: 0 16px 16px 0;
  z-index: 1;
}

/* 动态阴影 */
.dynamic-shadow {
  position: absolute;
  top: 0;
  left: -1px;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(183, 86, 255, 0.1) 0%,
    transparent 100%
  );
  pointer-events: none;
  transition: all 0.1s ease;
}

/* 翻页动画 */
.flip-page.flipping {
  animation: none;
}

.flip-page.flipping.flip-forward {
  animation: flip-forward 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.flip-page.flipping.flip-backward {
  animation: flip-backward 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 向前翻页动画 */
@keyframes flip-forward {
  0% {
    transform: rotateY(0deg) translateZ(0px);
  }
  15% {
    transform: rotateY(-5deg) translateZ(5px) scale(1.02);
  }
  50% {
    transform: rotateY(-90deg) translateZ(8px) scale(1.05);
  }
  85% {
    transform: rotateY(-175deg) translateZ(5px) scale(1.02);
  }
  100% {
    transform: rotateY(-180deg) translateZ(0px);
  }
}

/* 向后翻页动画 */
@keyframes flip-backward {
  0% {
    transform: rotateY(-180deg) translateZ(0px);
  }
  15% {
    transform: rotateY(-175deg) translateZ(5px) scale(1.02);
  }
  50% {
    transform: rotateY(-90deg) translateZ(8px) scale(1.05);
  }
  85% {
    transform: rotateY(-5deg) translateZ(5px) scale(1.02);
  }
  100% {
    transform: rotateY(0deg) translateZ(0px);
  }
}

/* 动态阴影动画 */
.flip-page.flipping .dynamic-shadow {
  animation: shadow-change 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes shadow-change {
  0% {
    opacity: 0.3;
    width: 2px;
  }
  50% {
    opacity: 0.8;
    width: 8px;
    background: linear-gradient(
      to right,
      rgba(183, 86, 255, 0.3) 0%,
      rgba(183, 86, 255, 0.1) 50%,
      transparent 100%
    );
  }
  100% {
    opacity: 0.3;
    width: 2px;
  }
}

/* Loading状态特殊处理 */
.diary-page.loading .page-content {
  justify-content: center;
  align-items: center;
  gap: 24px;
}

/* 下一页预览样式 */
.next-page-preview {
  opacity: 0.6;
  filter: blur(0.5px);
}

/* 日记头部 */
.diary-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  flex-shrink: 0;
}

.diary-title {
  color: #b756ff;

  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  text-align: left;
}

.diary-meta {
  display: flex;
  gap: 10px;
  align-items: center;
}

.diary-date,
.diary-weather {
  color: rgba(0, 0, 0, 0.5);

  font-weight: 600;
  font-size: 10px;
  line-height: 14px;
}

/* Loading 动画 */
.diary-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #666;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: #b756ff;
  opacity: 0.8;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: #b756ff;
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}
.loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-pen {
  position: relative;
  width: 60px;
  height: 8px;
  animation: pen-write 2s ease-in-out infinite;
}

.pen-tip {
  position: absolute;
  left: 0;
  top: 0;
  width: 8px;
  height: 8px;
  background: #333;
  border-radius: 0 50% 50% 50%;
  transform: rotate(-45deg);
}

.pen-body {
  position: absolute;
  left: 8px;
  top: 2px;
  width: 52px;
  height: 4px;
  background: linear-gradient(to right, #4169e1, #6495ed);
  border-radius: 2px;
}

@keyframes pen-write {
  0%,
  100% {
    transform: translateX(0) rotate(0deg);
  }
  25% {
    transform: translateX(10px) rotate(2deg);
  }
  50% {
    transform: translateX(20px) rotate(-1deg);
  }
  75% {
    transform: translateX(15px) rotate(1deg);
  }
}

/* 日记内容区域 */
.diary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
  padding-bottom: 20px; /* 增加底部间距，确保最后几行可见 */
}

.diary-lines {
  flex: 1;
  position: relative;
  padding: 8px 0 20px 0; /* 增加底部padding */
  background-image: repeating-linear-gradient(
    transparent,
    transparent 31px,
    rgba(0, 0, 0, 0.1) 31px,
    rgba(0, 0, 0, 0.1) 32px
  );
  background-position: 0 8px;
  overflow: hidden;
  min-height: 200px; /* 设置最小高度 */
}

.diary-text {
  color: #333;
  font-family: 'Courier New', monospace;
  font-weight: 400;
  font-size: 12px;
  line-height: 32px;
  text-align: left;
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  margin: 0;
  padding: 0 0 10px 0; /* 增加底部padding */
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  min-height: fit-content; /* 确保内容完整显示 */
}

/* 翻页控制 */
.page-controls {
  position: absolute;
  bottom: 12px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow:
    0 4px 20px rgba(183, 86, 255, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(183, 86, 255, 0.1);
  z-index: 3;
}

.page-button {
  width: 32px;
  height: 32px;
  border: none;
  background: linear-gradient(135deg, #b756ff 0%, #9c4ae5 100%);
  color: white;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(183, 86, 255, 0.3),
    0 1px 3px rgba(183, 86, 255, 0.2);
}

.page-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #9c4ae5 0%, #8a3fd1 100%);
  transform: translateY(-1px) scale(1.05);
  box-shadow:
    0 4px 12px rgba(183, 86, 255, 0.4),
    0 2px 6px rgba(183, 86, 255, 0.3);
}

.page-button:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
}

.page-button:disabled {
  background: rgba(183, 86, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page-indicator {
  font-size: 12px;
  font-weight: 600;
  color: #b756ff;
  white-space: nowrap;
  min-width: 50px;
  text-align: center;
  background: rgba(183, 86, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
}

.diary-decoration {
  position: absolute;
  top: 0;
  right: 30px;
  width: 27px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
